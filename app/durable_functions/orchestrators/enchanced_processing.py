import logging
from typing import Any

import azure.durable_functions as df
from azure.durable_functions import DurableOrchestrationContext

from constants.durable_functions import ActivityName, EventType, OrchestratorName
from constants.extracted_data import DataSourceType
from durable_functions.activities.models import (
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    FieldExtractionTask,
    ListBlobsActivityInput,
    ListBlobsActivityOutput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SummarizeOtherFieldActivityInput,
    SummarizeOtherFieldActivityOutput,
)

from .models import EnhancedProcessingInput


logger = logging.getLogger(__name__)
bp = df.Blueprint()


# Constants for better maintainability
MAX_PROGRESS_PERCENT = 99


@bp.entity_trigger(context_name='context')
def progress_tracker_entity(context: df.DurableEntityContext):
    """A Durable Entity to track the progress of a long-running operation.

    This entity maintains the state of a process that involves multiple steps,
    allowing for centralized and monotonic progress tracking even when tasks
    are executed in parallel.

    State:
        total_items (int): The total number of items to be processed.
        processed_items (int): The number of items that have been completed.
        message_ids (list[str]): A list of message_ids associated with the operation.
        last_reported_percent (int): The last reported progress percentage.

    Operations:
        initialize: Sets up the initial state with total items and message IDs.
        increment: Increments the count of processed items and returns the new
                   progress percentage and associated message IDs if the
                   percentage has changed.
        reset: Clears the entity's state.
    """
    state = context.get_state(lambda: {'total_items': 0, 'processed_items': 0, 'message_ids': [], 'last_reported_percent': -1})
    operation = context.operation_name

    if operation == 'initialize':
        payload = context.get_input()
        state = {
            'total_items': payload.get('total_items', 0),
            'processed_items': 0,
            'message_ids': payload.get('message_ids', []),
            'last_reported_percent': -1,
        }
        context.set_state(state)

    elif operation == 'increment':
        state['processed_items'] += 1

        current_progress = 0
        if state['total_items'] > 0:
            progress_fraction = state['processed_items'] / state['total_items']
            current_progress = min(int(round(progress_fraction * MAX_PROGRESS_PERCENT)), MAX_PROGRESS_PERCENT)

        if current_progress > state.get('last_reported_percent', -1):
            state['last_reported_percent'] = current_progress
            context.set_result({'percent': current_progress, 'message_ids': state['message_ids']})
        else:
            context.set_result(None)
        context.set_state(state)

    elif operation == 'reset':
        context.set_state({'total_items': 0, 'processed_items': 0, 'message_ids': [], 'last_reported_percent': -1})


def _create_extraction_tasks(summarized_content: str) -> list[FieldExtractionTask]:
    """Create a list of field extraction tasks based on summarized content."""
    return [
        FieldExtractionTask(
            field_name='engagement_summary',
            context=summarized_content,
            system_prompt='Extract the engagement_summary from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
        FieldExtractionTask(
            field_name='one_line_description',
            context=summarized_content,
            system_prompt='Extract the one_line_description from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
    ]


def _create_progress_notification_input(
    signalr_user_id: str, message_id: str, progress_percent: int
) -> SendNotificationActivityInput:
    """Create a progress notification input for SignalR."""
    return SendNotificationActivityInput(
        event_type=EventType.DraftQualProgress,
        data={'percent': progress_percent, 'message_id': message_id},
        signalr_user_id=signalr_user_id,
    )


@bp.orchestration_trigger('context', OrchestratorName.EnchancedProcessingSubOrchestrator)
def process_message(context: DurableOrchestrationContext):
    """Orchestrate the enhanced processing for a single message ID.

    This function processes a single message by listing its associated blobs,
    processing each one, and signaling a central entity to update the overall progress.

    Args:
        context: The durable orchestration context containing input data.
    """
    input_dict = context.get_input()
    if not input_dict:
        logger.error('No input provided for enhanced processing sub-orchestrator')
        return

    try:
        message_id = input_dict['message_id']
        signalr_user_id = input_dict.get('signalr_user_id')
        entity_id_name = input_dict['entity_id_name']
        entity_id_key = input_dict['entity_id_key']
        entity_id = df.EntityId(entity_id_name, entity_id_key)
    except KeyError as e:
        logger.error(f'Missing required input field: {e}')
        return

    prefix = f'chunks_extracted/{message_id}/'.lower()
    blobs_outputs: list[ListBlobsActivityOutput] = yield context.call_activity(
        ActivityName.ListBlobs, ListBlobsActivityInput(prefix=prefix)
    )

    for blob_output in blobs_outputs:
        if not blob_output.chunks:
            continue

        summarize_input = SummarizeOtherFieldActivityInput(
            conversation_id=message_id, chunks_extracted=blob_output.chunks
        )
        summarized_result: SummarizeOtherFieldActivityOutput = yield context.call_activity(
            ActivityName.SummarizeOtherField, summarize_input
        )

        if summarized_result and summarized_result.summarized_other_content:
            tasks = _create_extraction_tasks(summarized_result.summarized_other_content)
            enhanced_extraction_input = EnhancedExtractionActivityInput(
                conversation_id=message_id, tasks=tasks, source=blob_output.source
            )
            enhanced_extraction_result: EnhancedExtractionActivityOutput = yield context.call_activity(
                ActivityName.EnhancedExtraction, enhanced_extraction_input
            )

            if enhanced_extraction_result and enhanced_extraction_result.extracted_data:
                save_extraction_input = SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=enhanced_extraction_result.extracted_data,
                    data_source_type=DataSourceType(blob_output.source),
                )
                yield context.call_activity(ActivityName.SaveExtractionData, save_extraction_input)

        # After processing a blob, increment the central progress tracker
        if signalr_user_id:
            progress_data: dict[str, Any] = yield context.call_entity(entity_id, 'increment')
            if progress_data:
                percent = progress_data.get('percent', 0)
                all_message_ids = progress_data.get('message_ids', [])
                notification_tasks = [
                    context.call_activity(
                        ActivityName.SendNotification,
                        _create_progress_notification_input(signalr_user_id, mid, percent),
                    )
                    for mid in all_message_ids
                ]
                yield context.task_all(notification_tasks)


@bp.orchestration_trigger(context_name='context', orchestration=OrchestratorName.EnchancedExtraction)
def enchanced_processing_orchestrator(context: DurableOrchestrationContext):
    """Main orchestrator for enhanced data processing.

    This orchestrator implements a fan-out/fan-in pattern to process multiple
    message IDs in parallel, using a Durable Entity to track overall progress.
    """
    orch_name = str(OrchestratorName.EnchancedExtraction)
    input_dict = context.get_input()

    if not input_dict:
        logger.error(f'No input provided for {orch_name}')
        return

    try:
        input_data = EnhancedProcessingInput.model_validate(input_dict)
    except Exception as e:
        logger.error(f'Invalid input data for {orch_name}: {e}')
        return

    if not input_data.message_ids:
        logger.error(f'No message IDs provided for {orch_name}')
        return

    # Send 0% progress for all message IDs before processing starts
    if input_data.signalr_user_id:
        initial_notification_tasks = [
            context.call_activity(
                ActivityName.SendNotification,
                _create_progress_notification_input(input_data.signalr_user_id, message_id, 0),
            )
            for message_id in input_data.message_ids
        ]
        yield context.task_all(initial_notification_tasks)

    try:
        # Determine the total number of blobs to process
        list_blob_tasks: list = [
            context.call_activity(
                ActivityName.ListBlobs,
                ListBlobsActivityInput(prefix=f'chunks_extracted/{message_id}/'.lower()),
            )
            for message_id in input_data.message_ids
        ]
        all_blob_outputs: list[list[ListBlobsActivityOutput]] = yield context.task_all(list_blob_tasks)
        total_blobs = sum(len(blobs) for blobs in all_blob_outputs)

        if total_blobs == 0:
            logger.info('No blobs to process.')
        else:
            # Initialize the progress tracker entity
            entity_id = df.EntityId('progress_tracker_entity', context.instance_id)
            yield context.call_entity(
                entity_id,
                'initialize',
                {'total_items': total_blobs, 'message_ids': input_data.message_ids},
            )

            # Fan out to sub-orchestrators
            processing_tasks = [
                context.call_sub_orchestrator(
                    OrchestratorName.EnchancedProcessingSubOrchestrator,
                    {
                        'message_id': message_id,
                        'signalr_user_id': input_data.signalr_user_id,
                        'entity_id_name': entity_id.name,
                        'entity_id_key': entity_id.key,
                    },
                )
                for message_id in input_data.message_ids
            ]
            yield context.task_all(processing_tasks)

            # Reset the entity after completion
            context.signal_entity(entity_id, 'reset')

        # Send 100% progress for all message IDs after processing completes
        if input_data.signalr_user_id:
            final_notification_tasks = [
                context.call_activity(
                    ActivityName.SendNotification,
                    _create_progress_notification_input(input_data.signalr_user_id, message_id, 100),
                )
                for message_id in input_data.message_ids
            ]
            yield context.task_all(final_notification_tasks)

        logger.info(f'Successfully completed {orch_name} for all message IDs.')

    except Exception:
        logger.exception(f'Error in {orch_name} orchestrator')

